from pydantic import BaseModel, field_validator, Field
from bson import ObjectId
from typing import Dict, Any, Optional
from datetime import datetime


class SeenInputResponse(BaseModel):
    id: str

    @field_validator("id")
    def id_is_valid(cls, v):
        assert ObjectId.is_valid(v), "ID is invalid"
        return v


class SeenResponse(BaseModel):
    success: bool

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
            }
        }


class LastUpdateResponse(BaseModel):
    last_update :str | None = None


class IamResponse(BaseModel):
    type: str
    sent_at: datetime
    received_at: datetime | None = None
    from_field: str = Field(alias="from")
    to: str
    additional_parameters: Dict[str, Any] = {}

    class Config:
        populate_by_name = True

        json_schema_extra = {
            "example": {
                "type": "notification",
                "sent_at": "2024-10-19T21:35:39+03:30",
                "received_at": None,
                "from": "system",
                "to": "989123456789",
                "additional_parameters": {
                    "priority": "high",
                    "category": "promotional"
                }
            }
        }


class IamResponseResponse(BaseModel):
    id: str
    type: str
    from_user: str = Field(alias="from")  # Map 'from' field to 'from_user'
    to: str
    sent_at: datetime
    received_at: Optional[datetime] = None
    additional_parameters: Dict[str, Any] = {}
    notify_options: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        populate_by_name = True

        json_schema_extra = {
            "example": {
                "id": "6942e2603e062792ed640269",
                "type": "notification",
                "from_user": "system",
                "to": "989123456769",
                "sent_at": "2069-10-19T21:35:39+0330",
                "received_at": None,
                "additional_parameters": {},
                "notify_options": {
                    "title": "New notification omad",
                    "body": "You have a new notification, just to check baba",
                }
            }
        }
# TODO How to check the content of  notify_options and even validate that ?

from .config import logger, redis, mongo_client, iam_service, iam_cache_service

# from db.mongo.repositories import IamTemplateRepository


async def get_logger():
    return logger


async def get_database():
    return await mongo_client.get_session()


async def get_redis():
    return redis


async def get_iam_service():
    return iam_service


async def get_iam_cache_service():
    return iam_cache_service


# async def get_template_repository():
#     db = await get_database()
#     return IamTemplateRepository(db)

from pydantic import BaseModel
from pymongo import MongoClient
from ngmi_logging.utils import process_time
from bson.objectid import ObjectId
from typing import List, Dict, Any, Optional, TypeVar, Generic, Type
from datetime import datetime

T = TypeVar('T', bound=BaseModel)

class MongoCollection(Generic[T]):

    
    def __init__(
        self,
        collection_name: str,
        session: MongoClient,
        schema: Optional[Type[T]] = None,
    ) -> None:

        self.collection_name = collection_name
        self.session = session
        self.schema = schema

    def format_document(self, document: Dict) -> Dict:
        result = document.copy()
        result["id"] = str(result.pop("_id"))
        return result

    def prepare_for_insert(self, document: Dict) -> Dict:
        if self.schema:
            # Validate document against schema
            self.schema(**document)
        return document

    def enrich_document_with_id(self, document: Dict, inserted_id: ObjectId) -> Dict:
        result = document.copy()
        result["id"] = str(inserted_id)
        return result

    async def create_indexes(self) -> None:
        pass

    @process_time()
    async def find_all(self) -> List[Dict]:
        documents = self.session.get_collection(self.collection_name).find({})
        result = []
        async for document in documents:
            result.append(self.format_document(document))
        return result

    @process_time()
    async def find(self, query: Dict) -> List[Dict]:
        documents = self.session.get_collection(self.collection_name).find(query)
        result = []
        async for document in documents:
            result.append(self.format_document(document))
        return result

    @process_time()
    async def count(self, query: Dict) -> int:
        count = await self.session.get_collection(self.collection_name).count_documents(query)
        return count if isinstance(count, int) else 0

    @process_time()
    async def find_with_pagination(self, query: Dict, skip: int, limit: int, sort_field: str = "_id", sort_direction: int = -1) -> List[Dict]:
        documents = (
            self.session.get_collection(self.collection_name)
            .find(query)
            .sort(sort_field, sort_direction)
            .skip(skip)
            .limit(limit)
        )
        result = []
        async for document in documents:
            result.append(self.format_document(document))
        return result

    @process_time()
    async def find_one(self, query: Dict) -> Optional[Dict]:
        document = await self.session.get_collection(self.collection_name).find_one(query)
        return self.format_document(document) if document else None

    @process_time()
    async def find_by_id(self, id: str) -> Optional[Dict]:
        return await self.find_one({"_id": ObjectId(id)})

    @process_time()
    async def insert_one(self, document: Dict) -> Dict:
        prepared_document = self.prepare_for_insert(document)
        result = await self.session.get_collection(self.collection_name).insert_one(prepared_document)
        return self.enrich_document_with_id(document, result.inserted_id)

    @process_time()
    async def update_one(self, query: Dict, data: Dict, **kwargs) -> bool:
        result = await self.session.get_collection(self.collection_name).update_one(
            query, {"$set": data}, **kwargs
        )
        return result.modified_count > 0

    @process_time()
    async def update_by_id(self, id: str, data: Dict, **kwargs) -> bool:
        return await self.update_one({"_id": ObjectId(id)}, data, **kwargs)

    @process_time()
    async def delete_one(self, query: Dict) -> bool:
        result = await self.session.get_collection(self.collection_name).delete_one(query)
        return result.deleted_count > 0

    @process_time()
    async def delete_by_id(self, id: str) -> bool:
        return await self.delete_one({"_id": ObjectId(id)})

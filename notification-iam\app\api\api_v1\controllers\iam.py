from typing import List
from fastapi import APIRouter, Depends, status
from pymongo import MongoClient
from ..schemas.iam import (
    SeenInputResponse,
    IamResponseResponse,
    LastUpdateResponse,
    SeenResponse,
)
from core.config import authorization
from core.dependencies import get_database, get_iam_service
from services import IamService

router = APIRouter()


@router.get(
    "/iams",
    status_code=status.HTTP_200_OK,
    response_model=List[IamResponseResponse],
)
async def iams(
    last_update: str = "",
    profile: dict = Depends(authorization),
    db: MongoClient = Depends(get_database),
    iam_service: IamService = Depends(get_iam_service),
) -> List[IamResponseResponse]:

    return await iam_service.get_iams(
        session=db,
        phone_number=profile["phone_number"],
        last_update=last_update,
    )


@router.get(
    "/last_update", status_code=status.HTTP_200_OK, response_model=LastUpdateResponse
)
async def last_update(
    profile: dict = Depends(authorization),
    iam_service: IamService = Depends(get_iam_service),
) -> LastUpdateResponse:

    last_update_value = await iam_service.get_last_update(profile["phone_number"])
    return LastUpdateResponse(last_update=last_update_value)


@router.put("/seen", status_code=status.HTTP_200_OK, response_model=SeenResponse)
async def seen(
    seen_input: SeenInputResponse,
    profile: dict = Depends(authorization),
    db: MongoClient = Depends(get_database),
    iam_service: IamService = Depends(get_iam_service),
) -> SeenResponse:

    is_success = await iam_service.mark_as_seen(
        session=db,
        iam_id=seen_input.id,
        phone_number=profile["phone_number"],
    )

    if is_success:
        return SeenResponse(success=is_success)

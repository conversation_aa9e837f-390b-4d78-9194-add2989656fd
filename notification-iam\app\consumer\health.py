from .config import rabbitmq_consumer
from fastapi import HTTPException, status
from ngmi_logging.utils import prevent_access_log


def rabbitmq_health():
    if not rabbitmq_consumer.is_healthy:
        return False
    return True


@prevent_access_log
async def live():
    if rabbitmq_health():
        return {"status": "OK"}
    raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE)


@prevent_access_log
async def ready():
    return {"status": "OK"}

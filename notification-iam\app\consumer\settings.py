import os
from typing import Optional
from pydantic_settings import BaseSettings
from uuid import uuid4

parent_dir = os.path.abspath(os.path.abspath(os.path.dirname(__file__)) + "/../")


class Settings(BaseSettings):
    SERVICE_NAME: Optional[str] = "notification-iam-consumer"
    ENVIRONMENT: Optional[str] = "development"

    WORKER_ID: str = uuid4().hex

    MONGODB_URL: str

    REDIS_PREFIX: str

    FLUENTD_HTTP_ENDPOINT: str
    FLUENTD_HTTP_PORT: str
    FLUENTD_HTTP_TAG: str
    FLUENTD_HTTP_TAG2: Optional[str] = "http-ngmi-additional"
    FLUENTD_HTTP_PROTOCOL: str = "http"

    REDIS_CLUSTER_NODES: str
    REDIS_CLUSTER_USERNAME: str
    REDIS_CLUSTER_PASSWORD: str

    RABBITMQ_URL: str

    class Config:
        case_sensitive = True


settings = Settings()

from .settings import settings
from db.mongo.database import MongoDBAsync<PERSON>lient
from ngmi_logging import Logger
from ngmi_rabbitmq import RabbitMQConsumer
from ngmi_redis import RedisClient
from services import IamService, CacheService
import json


logger = Logger(
    url=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG}",
    aurl=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG2}",
    service_name=settings.SERVICE_NAME,
    worker_id=settings.WORKER_ID,
    console=True,
)

redis = RedisClient(
    nodes=json.loads(settings.REDIS_CLUSTER_NODES),
    username=settings.REDIS_CLUSTER_USERNAME,
    password=settings.REDIS_CLUSTER_PASSWORD,
    logger=logger,
    prefix=settings.REDIS_PREFIX,
)

mongo_client = MongoDBAsyncClient(url=settings.MONGODB_URL)

rabbitmq_consumer = RabbitMQConsumer(
    url=settings.RABBITMQ_URL,
    service_name=settings.SERVICE_NAME,
    logger=logger,
    prefetch_count=10,
)

cache_service = CacheService(redis_client=redis)
iam_service = IamService(cache_service=cache_service)
